示例：当你点击"自动获取(940,915)九点"按钮并输入按键"q"后，生成的脚本将包含类似这样的内容：

;
#NoEnv
#Warn
SendMode Input
SetWorkingDir %A_ScriptDir%

XButton1::
SetTimer,CheckColors, 0
KeyWait, XButton1
SetTimer,CheckColors, Off
Return

CheckColors:
    if ColorCheck9Points(940, 915, "A1B2", "A1B3", "A1B1", "A0B2", "A2B2", "A0B1", "A2B1", "A0B3", "A2B3", 5):
            press_key('q')    
Return

GetColor(x,y)
{
    CoordMode, Pixel, Screen
    PixelGetColor, color, x, y, RGB
    StringRight, color, color, 4
    return color
}

说明：
- ColorCheck9Points(940, 915, ..., 5) 中的9个颜色值是按照以下顺序排列的：
  1. 中心点 (940, 915)
  2. 上方 (940, 912)  
  3. 下方 (940, 918)
  4. 左方 (937, 915)
  5. 右方 (943, 915)
  6. 左上 (937, 912)
  7. 右上 (943, 912)
  8. 左下 (937, 918)
  9. 右下 (943, 918)

- 最后的参数5表示至少需要5个点匹配才触发按键
- 实际的颜色值会根据屏幕上的实际像素颜色自动获取
