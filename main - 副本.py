import pywintypes
import random
import time
import tkinter
import win32api
import win32con
import sys

# 导入PIL进行截屏
try:
    from PIL import ImageGrab
    print("成功导入PIL")
except ImportError:
    try:
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pillow"])
        from PIL import ImageGrab
        print("已安装并导入PIL")
    except Exception as e:
        print(f"无法安装PIL: {e}")
        print("请手动运行: pip install pillow")
        sys.exit(1)

# 导入pynput模块
try:
    from pynput import keyboard
    from pynput import mouse
    print("成功导入pynput")
except ImportError:
    try:
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pynput"])
        from pynput import keyboard
        from pynput import mouse
        print("已安装并导入pynput")
    except Exception as e:
        print(f"无法安装pynput: {e}")
        print("请手动运行: pip install pynput")
        sys.exit(1)

# 创建键盘和鼠标控制器
keyboard_controller = keyboard.Controller()
mouse_controller = mouse.Controller()

# 替代pyautogui.press()函数
def press_key(key):
    try:
        # 处理函数键和特殊键
        if key.startswith('f') and key[1:].isdigit():
            # 处理功能键 (F1-F12)
            f_num = int(key[1:])
            if 1 <= f_num <= 12:
                special_key = getattr(keyboard.Key, f'f{f_num}')
                keyboard_controller.press(special_key)
                keyboard_controller.release(special_key)
                print(f"按下功能键: {key}")
                return
        elif key == 'esc':
            keyboard_controller.press(keyboard.Key.esc)
            keyboard_controller.release(keyboard.Key.esc)
            print(f"按下按键: {key}")
            return
        elif key == 'tab':
            keyboard_controller.press(keyboard.Key.tab)
            keyboard_controller.release(keyboard.Key.tab)
            print(f"按下按键: {key}")
            return
        elif key == 'shift':
            keyboard_controller.press(keyboard.Key.shift)
            keyboard_controller.release(keyboard.Key.shift)
        elif key == 'ctrl':
            keyboard_controller.press(keyboard.Key.ctrl)
            keyboard_controller.release(keyboard.Key.ctrl)
        elif key == 'alt':
            keyboard_controller.press(keyboard.Key.alt)
            keyboard_controller.release(keyboard.Key.alt)
        elif key == 'space':
            keyboard_controller.press(keyboard.Key.space)
            keyboard_controller.release(keyboard.Key.space)
        elif key == 'enter':
            keyboard_controller.press(keyboard.Key.enter)
            keyboard_controller.release(keyboard.Key.enter)
            
        # 处理常规按键
        keyboard_controller.press(key)
        keyboard_controller.release(key)
        print(f"按下按键: {key}")
    except Exception as e:
        print(f"按键模拟失败: {key}, 错误: {e}")
        # 尝试备用方法
        try:
            # 对于单个字符的按键
            if len(key) == 1:
                keyboard_controller.press(key)
                keyboard_controller.release(key)
                print(f"备用方法按下按键: {key}")
            else:
                print(f"无法按下按键: {key}")
        except Exception as e2:
            print(f"备用方法也失败: {e2}")

# 替代pyautogui.moveTo()函数
def move_mouse(x, y):
    try:
        mouse_controller.position = (x, y)
        print(f"鼠标移动到: {x}, {y}")
    except Exception as e:
        print(f"鼠标移动失败: {e}")

# 替代pyautogui.click()函数
def click_mouse(x, y, button='left'):
    current_pos = mouse_controller.position
    try:
        mouse_controller.position = (x, y)
        if button == 'left':
            mouse_controller.click(mouse.Button.left)
        elif button == 'right':
            mouse_controller.click(mouse.Button.right)
        print(f"鼠标点击: {x}, {y}, 按钮: {button}")
    except Exception as e:
        print(f"鼠标点击失败: {e}")
    finally:
        # 无论是否发生异常，都确保鼠标移回原位
        try:
            mouse_controller.position = current_pos
        except Exception as e:
            print(f"恢复鼠标位置失败: {e}")

label = tkinter.Label(text='Holding', font=('Times', '50'), fg='blue', bg='white')
label.master.overrideredirect(True)
label.master.geometry("+250+250")
label.master.lift()
label.master.wm_attributes("-topmost", True)
label.master.wm_attributes("-disabled", True)
label.master.wm_attributes("-transparentcolor", "white")
hWindow = pywintypes.HANDLE(int(label.master.frame(), 16))
# http://msdn.microsoft.com/en-us/library/windows/desktop/ff700543(v=vs.85).aspx
# The WS_EX_TRANSPARENT flag makes events (like mouse clicks) fall through the window.
exStyle = win32con.WS_EX_COMPOSITED | win32con.WS_EX_LAYERED | win32con.WS_EX_NOACTIVATE | win32con.WS_EX_TOPMOST | win32con.WS_EX_TRANSPARENT
win32api.SetWindowLong(hWindow, win32con.GWL_EXSTYLE, exStyle)
label.pack()

timerRunning = 0
key_2 = 0
key_3 = 0
key_q = 0
key_f1 = 0
key_f = 0
debug_mode = False  # 调试模式开关





def GetColor(x, y):
    try:
        # 使用PIL截取一个1x1像素的屏幕区域
        img = ImageGrab.grab(bbox=(x, y, x+1, y+1))
        color = img.getpixel((0, 0))  # 获取该像素的颜色
        # 转换为十六进制并取后4位
        color_hex = ''.join([format(c, '02X') for c in color[:3]])[-4:]

        # 调试模式下输出像素颜色信息
        if debug_mode:
            print(f"位置 ({x}, {y}) 的颜色: RGB{color} -> HEX: {color_hex}")

        return color_hex
    except Exception as e:
        print(f"无法获取屏幕像素颜色: {e}")
        return "ERROR"


def ColorMatch(actual_color, expected_color, tolerance=5):
    """
    比较两个颜色是否在指定的误差范围内匹配
    actual_color: 实际获取的颜色 (4位十六进制字符串)
    expected_color: 期望的颜色 (4位十六进制字符串)
    tolerance: 允许的误差值 (默认为5)
    """
    if actual_color == "ERROR" or expected_color == "ERROR":
        return False

    try:
        # 将4位十六进制颜色转换为两个字节的值
        def hex_to_bytes(hex_color):
            # 确保是4位十六进制
            if len(hex_color) == 4:
                # 将4位十六进制分解为两个字节
                byte1 = int(hex_color[0:2], 16)  # 前两位
                byte2 = int(hex_color[2:4], 16)  # 后两位
                return (byte1, byte2)
            else:
                return (0, 0)

        actual_bytes = hex_to_bytes(actual_color)
        expected_bytes = hex_to_bytes(expected_color)

        # 检查每个字节是否在误差范围内
        for i in range(2):
            if abs(actual_bytes[i] - expected_bytes[i]) > tolerance:
                return False

        return True
    except Exception as e:
        print(f"颜色比较错误: {e}")
        return False


def ColorCheck(x, y, ref):
    if (ColorMatch(GetColor(x, y), ref) and ColorMatch(GetColor(x - 3, y), ref) and ColorMatch(GetColor(x + 3, y), ref) and
            ColorMatch(GetColor(x, y - 3), ref) and ColorMatch(GetColor(x, y + 3), ref)):
        return [x, y]
    else:
        return False


def ColorCheckFlexible(points_colors, min_matches=4):
    """
    检查多个点的颜色，如果满足最小匹配数量就返回True

    参数:
    points_colors: 包含(x, y, expected_color)元组的列表
    min_matches: 最少需要匹配的点数量，默认为4

    返回:
    如果匹配的点数量 >= min_matches，返回True，否则返回False
    """
    if len(points_colors) < min_matches:
        return False

    match_count = 0
    for x, y, expected_color in points_colors:
        if ColorMatch(GetColor(x, y), expected_color):
            match_count += 1
            # 如果已经达到最小匹配数量，可以提前返回
            if match_count >= min_matches:
                return True

    return False


def ColorCheck7Points(center_x, center_y, ref, min_matches=4):
    """
    检查7个点的颜色匹配情况：中心点 + 上下左右各3像素 + 对角线各3像素
    如果7个点中有任意4个（或指定数量）满足ColorMatch就返回True

    参数:
    center_x, center_y: 中心点坐标
    ref: 期望的颜色值
    min_matches: 最少需要匹配的点数量，默认为4

    返回:
    如果匹配的点数量 >= min_matches，返回True，否则返回False
    """
    # 定义7个检查点：中心点 + 上下左右 + 左上右下对角线
    points = [
        (center_x, center_y),           # 中心点
        (center_x, center_y - 3),       # 上
        (center_x, center_y + 3),       # 下
        (center_x - 3, center_y),       # 左
        (center_x + 3, center_y),       # 右
        (center_x - 3, center_y - 3),   # 左上
        (center_x + 3, center_y + 3),   # 右下
    ]

    match_count = 0
    for x, y in points:
        if ColorMatch(GetColor(x, y), ref):
            match_count += 1
            # 如果已经达到最小匹配数量，可以提前返回
            if match_count >= min_matches:
                return True

    return False


# 使用示例函数
def test_ColorCheck7Points():
    """
    测试ColorCheck7Points函数的使用示例
    """
    # 示例：检查某个位置的颜色，7个点中任意4个匹配就返回True
    center_x, center_y = 1140, 620
    expected_color = "FBFF"

    if ColorCheck7Points(center_x, center_y, expected_color, min_matches=4):
        print(f"在位置({center_x}, {center_y})附近找到了足够的匹配点")
        return True
    else:
        print(f"在位置({center_x}, {center_y})附近没有找到足够的匹配点")
        return False


def Heal_Raid_Frames_Check():
    # 红色
    red = '0011'
    yellow = 'FF00'
    blue = '1EFF'

    # 外层循环变量初始化
    start_x = 100
    increment_x = 70
    iterations_x = 8

    # 内层循环变量初始化
    start_y = 575
    increment_y = 35
    iterations_y = 5

    # 双层for循环
    red_list = []
    yellow_list = []
    blue_list = []
    st_aoe = ''

    for i in range(iterations_x):
        x_value = start_x + i * increment_x
        for j in range(iterations_y):
            y_value = start_y + j * increment_y
            res = ColorCheck(x_value, y_value, red)
            if res:
                red_list.append([res[0] + 20, res[1]])
            res = ColorCheck(x_value, y_value, yellow)
            if res:
                yellow_list.append([res[0] + 20, res[1]])
            res = ColorCheck(x_value, y_value, blue)
            if res:
                blue_list.append([res[0] + 20, res[1]])

    length = len(red_list) + len(yellow_list) + len(blue_list)
    if length >= 3:
        st_aoe = 'AOE'
    if length == 1:
        st_aoe = 'ST'
    if length == 0:
        st_aoe = 'STOP'
    return st_aoe, red_list, yellow_list, blue_list

def Priest_Discipline():
    if (ColorMatch(GetColor(1135,623),"E773") and ColorMatch(GetColor(1132,623),"FBAD") and ColorMatch(GetColor(1138,623),"E794") and ColorMatch(GetColor(1135,620),"ED8F") and ColorMatch(GetColor(1135,626),"ED5D")):
        press_key('f11')
    if (ColorMatch(GetColor(1140,621),"FBFF") and ColorMatch(GetColor(1137,621),"FBFF") and ColorMatch(GetColor(1143,621),"FFFF") and ColorMatch(GetColor(1140,618),"FDFF") and ColorMatch(GetColor(1140,624),"FBFF")):
        press_key('f9')
    if (ColorMatch(GetColor(1141,622),"9C7F") and ColorMatch(GetColor(1138,622),"D9B1") and ColorMatch(GetColor(1144,622),"A681") and ColorMatch(GetColor(1141,619),"DFBD") and ColorMatch(GetColor(1141,625),"B287")):
        press_key('f12')
    if (ColorMatch(GetColor(1135,621),"EDE9") and ColorMatch(GetColor(1132,621),"EDE7") and ColorMatch(GetColor(1138,621),"D7D6") and ColorMatch(GetColor(1135,618),"EEE9") and ColorMatch(GetColor(1135,624),"EEE9")):
        press_key('f10')
    if (ColorMatch(GetColor(1133,619),"FF77") and ColorMatch(GetColor(1130,619),"FF31") and ColorMatch(GetColor(1136,619),"FFC5") and ColorMatch(GetColor(1133,616),"FF29") and ColorMatch(GetColor(1133,622),"FFC1")):
        press_key('f8')


def Priest_Discipline_Enhanced():
    """
    使用新的ColorCheck7Points函数的增强版牧师戒律检测
    7个点中任意4个满足ColorMatch就触发技能
    """
    # 技能1：使用7点检测，需要4个点匹配
    if ColorCheck7Points(1135, 623, "E773", min_matches=4):
        press_key('f11')
        return

    # 技能2：使用7点检测，需要4个点匹配
    if ColorCheck7Points(1140, 621, "FBFF", min_matches=4):
        press_key('f9')
        return

    # 技能3：使用7点检测，需要4个点匹配
    if ColorCheck7Points(1141, 622, "9C7F", min_matches=4):
        press_key('f12')
        return

    # 技能4：使用7点检测，需要4个点匹配
    if ColorCheck7Points(1135, 621, "EDE9", min_matches=4):
        press_key('f10')
        return

    # 技能5：使用7点检测，需要4个点匹配
    if ColorCheck7Points(1133, 619, "FF77", min_matches=4):
        press_key('f8')
        return



def Shaman_Restoration():
    '''
    st_aoe, red_list, yellow_list, blue_list = Heal_Raid_Frames_Check()

    if st_aoe != 'STOP':
        if len(red_list) != 0:
            lucky_guy = random.choice(red_list)
        elif len(yellow_list) != 0:
            lucky_guy = random.choice(yellow_list)
        elif len(blue_list) != 0:
            lucky_guy = random.choice(blue_list)
        print(lucky_guy)
        if (GetColor(1138, 625) == "5F63") and (GetColor(1135, 625) == "CDD0") and (GetColor(1141, 625) == "666B") and (
                GetColor(1138, 622) == "8B8A") and (GetColor(1138, 628) == "545A"):
            curr_position = pyautogui.position()
            print(curr_position)
            pyautogui.moveTo(lucky_guy[0], lucky_guy[1])
            pyautogui.press('f2')
            pyautogui.moveTo(curr_position)
            return
        if st_aoe == 'ST':
            curr_position = pyautogui.position()
            pyautogui.click(x=lucky_guy[0], y=lucky_guy[1], button='left')
            return
        if st_aoe == 'AOE':
            curr_position = pyautogui.position()
            pyautogui.click(x=lucky_guy[0], y=lucky_guy[1], button='right')

            return
    '''

def Rogue_Assasination():
    if (ColorMatch(GetColor(1138, 641), "7984") and ColorMatch(GetColor(1135, 641), "7984") and ColorMatch(GetColor(1141, 641), "7584") and
            ColorMatch(GetColor(1138, 638), "697A") and ColorMatch(GetColor(1138, 644), "7581")):
        press_key('t')
    if (ColorMatch(GetColor(1140, 638), "252F") and ColorMatch(GetColor(1137, 638), "3C3E") and ColorMatch(GetColor(1143, 638), "6C7B") and
            ColorMatch(GetColor(1140, 635), "2C31") and ColorMatch(GetColor(1140, 641), "2418")):
        press_key('f6')
    if (ColorMatch(GetColor(1142, 642), "656B") and ColorMatch(GetColor(1139, 642), "CFCE") and ColorMatch(GetColor(1145, 642), "FBFF") and
            ColorMatch(GetColor(1142, 639), "FBF8") and ColorMatch(GetColor(1142, 645), "9C9F")):
        press_key('2')
    if (ColorMatch(GetColor(1141, 640), "0000") and ColorMatch(GetColor(1138, 640), "0000") and ColorMatch(GetColor(1144, 640), "0000") and
            ColorMatch(GetColor(1141, 637), "0000") and ColorMatch(GetColor(1141, 643), "0000")):
        press_key('1')
    if (ColorMatch(GetColor(1143, 640), "F3F7") and ColorMatch(GetColor(1140, 640), "5E63") and ColorMatch(GetColor(1146, 640), "565C") and
            ColorMatch(GetColor(1143, 637), "5E63") and ColorMatch(GetColor(1143, 643), "686E")):
        press_key('2')
    if (ColorMatch(GetColor(1138, 642), "3E91") and ColorMatch(GetColor(1135, 642), "61AD") and ColorMatch(GetColor(1141, 642), "4297") and
            ColorMatch(GetColor(1138, 639), "49A8") and ColorMatch(GetColor(1138, 645), "3B99")):
        press_key('4')
    if (ColorMatch(GetColor(1141, 635), "DCEC") and ColorMatch(GetColor(1138, 635), "F2FF") and ColorMatch(GetColor(1144, 635), "C7CE") and
            ColorMatch(GetColor(1141, 632), "C3CE") and ColorMatch(GetColor(1141, 638), "E7EF")):
        press_key('3')
    if (ColorMatch(GetColor(1137, 640), "E2CF") and ColorMatch(GetColor(1134, 640), "DA62") and ColorMatch(GetColor(1140, 640), "A196") and
            ColorMatch(GetColor(1137, 637), "DECF") and ColorMatch(GetColor(1137, 643), "FFFF")):
        press_key('r')
    if (ColorMatch(GetColor(1139, 642), "149C") and ColorMatch(GetColor(1136, 642), "107B") and ColorMatch(GetColor(1142, 642), "B7BD") and
            ColorMatch(GetColor(1139, 639), "1884") and ColorMatch(GetColor(1139, 645), "C5A4")):
        press_key('q')
    if (ColorMatch(GetColor(1138, 641), "5F5E") and ColorMatch(GetColor(1135, 641), "0000") and ColorMatch(GetColor(1141, 641), "E3E7") and
            ColorMatch(GetColor(1138, 638), "5B60") and ColorMatch(GetColor(1138, 644), "EBEA")):
        press_key('f3')
    if (ColorMatch(GetColor(1141, 642), "C39C") and ColorMatch(GetColor(1138, 642), "7F69") and ColorMatch(GetColor(1144, 642), "CDC5") and
            ColorMatch(GetColor(1141, 639), "7C6B") and ColorMatch(GetColor(1141, 645), "8252")):
        press_key('5')
    if (ColorMatch(GetColor(1139, 642), "3018") and ColorMatch(GetColor(1136, 642), "1E0D") and ColorMatch(GetColor(1142, 642), "2F18") and
            ColorMatch(GetColor(1139, 639), "381C") and ColorMatch(GetColor(1139, 645), "3F20")):
        press_key('f5')

def Rogue_Outlaw():
    if (ColorMatch(GetColor(1129, 628), "6D31") and ColorMatch(GetColor(1126, 628), "4715") and ColorMatch(GetColor(1132, 628), "2605") and
            ColorMatch(GetColor(1129, 625), "150D") and ColorMatch(GetColor(1129, 631), "5510")):
        press_key('f1')
    if (ColorMatch(GetColor(1143, 623), "221B") and ColorMatch(GetColor(1140, 623), "2520") and ColorMatch(GetColor(1146, 623), "241B") and
            ColorMatch(GetColor(1143, 620), "A23F") and ColorMatch(GetColor(1143, 626), "3029")):
        press_key('f3')
    if (ColorMatch(GetColor(1140, 619), "B68C") and ColorMatch(GetColor(1137, 619), "635E") and ColorMatch(GetColor(1143, 619), "C589") and
            ColorMatch(GetColor(1140, 616), "E7C6") and ColorMatch(GetColor(1140, 622), "5952")):
        press_key('5')
    if (ColorMatch(GetColor(1138, 623), "450D") and ColorMatch(GetColor(1135, 623), "BF59") and ColorMatch(GetColor(1141, 623), "400D") and
            ColorMatch(GetColor(1138, 620), "5118") and ColorMatch(GetColor(1138, 626), "0008")):
        press_key('f5')
    if (ColorMatch(GetColor(1140, 625), "DF77") and ColorMatch(GetColor(1137, 625), "DC81") and ColorMatch(GetColor(1143, 625), "DEB2") and
            ColorMatch(GetColor(1140, 622), "EDA4") and ColorMatch(GetColor(1140, 628), "BE56")):
        press_key('f4')
    if (ColorMatch(GetColor(1138, 622), "7684") and ColorMatch(GetColor(1135, 622), "8690") and ColorMatch(GetColor(1141, 622), "7381") and
            ColorMatch(GetColor(1138, 619), "6973") and ColorMatch(GetColor(1138, 625), "7A84")):
        press_key('t')
    if (ColorMatch(GetColor(1140, 618), "4D52") and ColorMatch(GetColor(1137, 618), "8481") and ColorMatch(GetColor(1143, 618), "8556") and
            ColorMatch(GetColor(1140, 615), "8B88") and ColorMatch(GetColor(1140, 621), "654C")):
        press_key('r')
    if (ColorMatch(GetColor(1138, 619), "AEE6") and ColorMatch(GetColor(1135, 619), "AFCE") and ColorMatch(GetColor(1141, 619), "FBFF") and
            ColorMatch(GetColor(1138, 616), "FFFF") and ColorMatch(GetColor(1138, 622), "4698")):
        press_key('f2')
    if (ColorMatch(GetColor(1141, 625), "0000") and ColorMatch(GetColor(1138, 625), "0000") and ColorMatch(GetColor(1144, 625), "0000") and
            ColorMatch(GetColor(1141, 622), "0000") and ColorMatch(GetColor(1141, 628), "0000")):
        press_key('f8')
    if (ColorMatch(GetColor(1141, 623), "8684") and ColorMatch(GetColor(1138, 623), "ACAA") and ColorMatch(GetColor(1144, 623), "798B") and
            ColorMatch(GetColor(1141, 620), "00E7") and ColorMatch(GetColor(1141, 626), "B9B9")):
        press_key('f6')

def Shaman_Enhancement():

        if (ColorMatch(GetColor(1142,623),"3046") and ColorMatch(GetColor(1139,623),"3C6A") and ColorMatch(GetColor(1145,623),"7F90") and ColorMatch(GetColor(1142,620),"9AC6") and ColorMatch(GetColor(1142,626),"8AA8")):
            press_key('f3')
        if (ColorMatch(GetColor(1140,623),"0C10") and ColorMatch(GetColor(1137,623),"8DB2") and ColorMatch(GetColor(1143,623),"B77E") and ColorMatch(GetColor(1140,620),"1000") and ColorMatch(GetColor(1140,626),"0608")):
            press_key('f4')
        if (ColorMatch(GetColor(1140,624),"2A1C") and ColorMatch(GetColor(1137,624),"1002") and ColorMatch(GetColor(1143,624),"2218") and ColorMatch(GetColor(1140,621),"6546") and ColorMatch(GetColor(1140,627),"454A")):
            press_key('f5')
        if (ColorMatch(GetColor(1140,623),"595D") and ColorMatch(GetColor(1137,623),"2A2E") and ColorMatch(GetColor(1143,623),"8EA0") and ColorMatch(GetColor(1140,620),"676F") and ColorMatch(GetColor(1140,626),"D1F3")):
            press_key('f6')
        if (ColorMatch(GetColor(1138,623),"1B36") and ColorMatch(GetColor(1135,623),"207B") and ColorMatch(GetColor(1141,623),"2691") and ColorMatch(GetColor(1138,620),"246F") and ColorMatch(GetColor(1138,626),"3ADA")):
            press_key('f7')
        if (ColorMatch(GetColor(1138,621),"027E") and ColorMatch(GetColor(1135,621),"0047") and ColorMatch(GetColor(1141,621),"08D6") and ColorMatch(GetColor(1138,618),"0252") and ColorMatch(GetColor(1138,624),"00D6")):
            press_key('f1')
        if (ColorMatch(GetColor(1139,621),"CA5E") and ColorMatch(GetColor(1136,621),"C75D") and ColorMatch(GetColor(1142,621),"C656") and ColorMatch(GetColor(1139,618),"AE39") and ColorMatch(GetColor(1139,624),"CB3D")):
            press_key('f9')
        if (ColorMatch(GetColor(1136,623),"DFE1") and ColorMatch(GetColor(1133,623),"5D48") and ColorMatch(GetColor(1139,623),"3031") and ColorMatch(GetColor(1136,620),"EFEF") and ColorMatch(GetColor(1136,626),"A1AF")):
            press_key('f8')
        if (ColorMatch(GetColor(1141,623),"4544") and ColorMatch(GetColor(1138,623),"8870") and ColorMatch(GetColor(1144,623),"4E3F") and ColorMatch(GetColor(1141,620),"6B4F") and ColorMatch(GetColor(1141,626),"5877")):
            press_key('f2')
        if (ColorMatch(GetColor(1142,626),"5B18") and ColorMatch(GetColor(1139,626),"C71C") and ColorMatch(GetColor(1145,626),"6F22") and ColorMatch(GetColor(1142,623),"B02E") and ColorMatch(GetColor(1142,629),"880C")):
            press_key('f12')
        if (ColorMatch(GetColor(1142,622),"FB97") and ColorMatch(GetColor(1139,622),"FBBE") and ColorMatch(GetColor(1145,622),"FDB5") and ColorMatch(GetColor(1142,619),"FCAA") and ColorMatch(GetColor(1142,625),"ED8F")):
            press_key('t')
        if (ColorMatch(GetColor(1142,622),"1109") and ColorMatch(GetColor(1139,622),"0808") and ColorMatch(GetColor(1145,622),"0C08") and ColorMatch(GetColor(1142,619),"0C08") and ColorMatch(GetColor(1142,625),"120B")):
            press_key('f10')
def Shaman_Elemental():
    if (ColorMatch(GetColor(1124, 612), "BBEF") and ColorMatch(GetColor(1121, 612), "AAD6") and ColorMatch(GetColor(1127, 612), "EDFB") and
            ColorMatch(GetColor(1124, 609), "D4F4") and ColorMatch(GetColor(1124, 615), "F8FC")):
        press_key('f7')
    if (ColorMatch(GetColor(1136, 622), "3A87") and ColorMatch(GetColor(1133, 622), "41A7") and ColorMatch(GetColor(1139, 622), "3981") and
            ColorMatch(GetColor(1136, 619), "6FCE") and ColorMatch(GetColor(1136, 625), "3073")):
        press_key('f10')
    if (ColorMatch(GetColor(1128, 609), "E0E5") and ColorMatch(GetColor(1125, 609), "ECF1") and ColorMatch(GetColor(1131, 609), "D6D3") and
            ColorMatch(GetColor(1128, 606), "737B") and ColorMatch(GetColor(1128, 612), "EDF1")):
        press_key('f8')
    if (ColorMatch(GetColor(1139, 619), "943F") and ColorMatch(GetColor(1136, 619), "7D24") and ColorMatch(GetColor(1142, 619), "BE6B") and
            ColorMatch(GetColor(1139, 616), "862B") and ColorMatch(GetColor(1139, 622), "CB7B")):
        press_key('r')
    if (ColorMatch(GetColor(1137, 623), "7D73") and ColorMatch(GetColor(1134, 623), "4D4A") and ColorMatch(GetColor(1140, 623), "756B") and
            ColorMatch(GetColor(1137, 620), "6D63") and ColorMatch(GetColor(1137, 626), "7B73")):
        press_key('3')
    if (ColorMatch(GetColor(1138, 619), "3C41") and ColorMatch(GetColor(1135, 619), "E3DE") and ColorMatch(GetColor(1141, 619), "504F") and
            ColorMatch(GetColor(1138, 616), "4547") and ColorMatch(GetColor(1138, 622), "323F")):
        press_key('f11')
    if (ColorMatch(GetColor(1135, 619), "0808") and ColorMatch(GetColor(1132, 619), "1818") and ColorMatch(GetColor(1138, 619), "1418") and
            ColorMatch(GetColor(1135, 616), "1113") and ColorMatch(GetColor(1135, 622), "1C29")):
        press_key('f12')
    if (ColorMatch(GetColor(1142, 624), "E3F7") and ColorMatch(GetColor(1139, 624), "E3FF") and ColorMatch(GetColor(1145, 624), "2852") and
            ColorMatch(GetColor(1142, 621), "E3F7") and ColorMatch(GetColor(1142, 627), "61A4")):
        press_key('-')
    if (ColorMatch(GetColor(1141, 619), "A747") and ColorMatch(GetColor(1138, 619), "903C") and ColorMatch(GetColor(1144, 619), "B256") and
            ColorMatch(GetColor(1141, 616), "A649") and ColorMatch(GetColor(1141, 622), "AC4E")):
        press_key('=')
    if (ColorMatch(GetColor(1143, 617), "0094") and ColorMatch(GetColor(1140, 617), "62DB") and ColorMatch(GetColor(1146, 617), "49FF") and
            ColorMatch(GetColor(1143, 614), "0077") and ColorMatch(GetColor(1143, 620), "03EE")):
        press_key('q')
    if (ColorMatch(GetColor(1142, 619), "6EDE") and ColorMatch(GetColor(1139, 619), "9AE9") and ColorMatch(GetColor(1145, 619), "FBFF") and
            ColorMatch(GetColor(1142, 616), "BCF4") and ColorMatch(GetColor(1142, 622), "A6FB")):
        press_key('f6')
    if (ColorMatch(GetColor(1141, 625), "5E08") and ColorMatch(GetColor(1138, 625), "7B18") and ColorMatch(GetColor(1144, 625), "470A") and
            ColorMatch(GetColor(1141, 622), "9221") and ColorMatch(GetColor(1141, 628), "7A20")):
        press_key('f7')
    if (ColorMatch(GetColor(1138, 622), "F7EF") and ColorMatch(GetColor(1135, 622), "F3FB") and ColorMatch(GetColor(1141, 622), "EDE2") and
            ColorMatch(GetColor(1138, 619), "EFDA") and ColorMatch(GetColor(1138, 625), "F7FF")):
        press_key('f8')
    if (ColorMatch(GetColor(1141, 621), "3A39") and ColorMatch(GetColor(1138, 621), "4449") and ColorMatch(GetColor(1144, 621), "3C39") and
            ColorMatch(GetColor(1141, 618), "6573") and ColorMatch(GetColor(1141, 624), "4652")):
        press_key('f9')
    if (ColorMatch(GetColor(1136, 621), "3147") and ColorMatch(GetColor(1133, 621), "1829") and ColorMatch(GetColor(1139, 621), "3E47") and
            ColorMatch(GetColor(1136, 618), "8579") and ColorMatch(GetColor(1136, 624), "2F73")):
        press_key('f10')
    if (ColorMatch(GetColor(1140, 625), "1B05") and ColorMatch(GetColor(1137, 625), "0200") and ColorMatch(GetColor(1143, 625), "5614") and
            ColorMatch(GetColor(1140, 622), "9225") and ColorMatch(GetColor(1140, 628), "0400")):
        press_key('f3')
    if (ColorMatch(GetColor(1139, 628), "BAF8") and ColorMatch(GetColor(1136, 628), "D5F5") and ColorMatch(GetColor(1142, 628), "C7E6") and
            ColorMatch(GetColor(1139, 625), "30D9") and ColorMatch(GetColor(1139, 631), "9DD9")):
        press_key('f1')
    if (ColorMatch(GetColor(1141, 620), "2F17") and ColorMatch(GetColor(1138, 620), "1C10") and ColorMatch(GetColor(1144, 620), "EF84") and
            ColorMatch(GetColor(1141, 617), "AB62") and ColorMatch(GetColor(1141, 623), "833D")):
        press_key('f5')
    if (ColorMatch(GetColor(1138, 625), "5723") and ColorMatch(GetColor(1135, 625), "AE4F") and ColorMatch(GetColor(1141, 625), "4518") and
            ColorMatch(GetColor(1138, 622), "7536") and ColorMatch(GetColor(1138, 628), "3E19")):
        press_key('f4')
    if (ColorMatch(GetColor(1140, 620), "6B4F") and ColorMatch(GetColor(1137, 620), "95B5") and ColorMatch(GetColor(1143, 620), "674D") and
            ColorMatch(GetColor(1140, 617), "8380") and ColorMatch(GetColor(1140, 623), "8870")):
        press_key('f2')
    if (ColorMatch(GetColor(1145,611),"6D0C") and ColorMatch(GetColor(1142,611),"0C08") and ColorMatch(GetColor(1148,611),"DF10") and ColorMatch(GetColor(1145,608),"1008") and ColorMatch(GetColor(1145,614),"620A")):
        press_key('f8')

def Deathknight_Unholy():
    if (ColorMatch(GetColor(1127,646),"5DAB") and ColorMatch(GetColor(1124,646),"65C0") and ColorMatch(GetColor(1130,646),"84CC") and ColorMatch(GetColor(1127,643),"1456") and ColorMatch(GetColor(1127,649),"225D")):
        press_key('2')
    if (ColorMatch(GetColor(1127,640),"3A39") and ColorMatch(GetColor(1124,640),"3130") and ColorMatch(GetColor(1130,640),"3A3B") and ColorMatch(GetColor(1127,637),"474A") and ColorMatch(GetColor(1127,643),"2F2F")):
        press_key('f')
    if (ColorMatch(GetColor(1128,645),"9819") and ColorMatch(GetColor(1125,645),"5F15") and ColorMatch(GetColor(1131,645),"6A13") and ColorMatch(GetColor(1128,642),"CD15") and ColorMatch(GetColor(1128,648),"7B13")):
        press_key('f11')
    if (ColorMatch(GetColor(1130,647),"733B") and ColorMatch(GetColor(1127,647),"9E53") and ColorMatch(GetColor(1133,647),"3316") and ColorMatch(GetColor(1130,644),"4017") and ColorMatch(GetColor(1130,650),"3A18")):
        press_key('f2')
    if (ColorMatch(GetColor(1131,647),"4738") and ColorMatch(GetColor(1128,647),"8A70") and ColorMatch(GetColor(1134,647),"4B43") and ColorMatch(GetColor(1131,644),"967B") and ColorMatch(GetColor(1131,650),"6653")):
        press_key('f3')
    if (ColorMatch(GetColor(1129,643),"804F") and ColorMatch(GetColor(1126,643),"955B") and ColorMatch(GetColor(1132,643),"6F43") and ColorMatch(GetColor(1129,640),"A563") and ColorMatch(GetColor(1129,646),"8A56")):
        press_key('f5')
    if (ColorMatch(GetColor(1126,648),"3F4E") and ColorMatch(GetColor(1123,648),"83A0") and ColorMatch(GetColor(1129,648),"9AB6") and ColorMatch(GetColor(1126,645),"869C") and ColorMatch(GetColor(1126,651),"4457")):
        press_key('f12')
    if (ColorMatch(GetColor(1130,644),"E8D7") and ColorMatch(GetColor(1127,644),"9F79") and ColorMatch(GetColor(1133,644),"F0DF") and ColorMatch(GetColor(1130,641),"A470") and ColorMatch(GetColor(1130,647),"652D")):
        press_key('f4')
    if (ColorMatch(GetColor(1129,647),"6059") and ColorMatch(GetColor(1126,647),"7370") and ColorMatch(GetColor(1132,647),"202B") and ColorMatch(GetColor(1129,644),"3B3C") and ColorMatch(GetColor(1129,650),"534F")):
        press_key('f9')
    if (ColorMatch(GetColor(1131,646),"1A11") and ColorMatch(GetColor(1128,646),"1C10") and ColorMatch(GetColor(1134,646),"180B") and ColorMatch(GetColor(1131,643),"3420") and ColorMatch(GetColor(1131,649),"5334")):
        press_key('f8')
    if (ColorMatch(GetColor(1127,646),"D4DA") and ColorMatch(GetColor(1124,646),"7BA8") and ColorMatch(GetColor(1130,646),"2869") and ColorMatch(GetColor(1127,643),"87CB") and ColorMatch(GetColor(1127,649),"3A87")):
        press_key('6')
    if (ColorMatch(GetColor(1128,646),"1E3C") and ColorMatch(GetColor(1125,646),"A6C7") and ColorMatch(GetColor(1131,646),"2336") and ColorMatch(GetColor(1128,643),"7A9C") and ColorMatch(GetColor(1128,649),"212D")):
        press_key('f7')
    if (ColorMatch(GetColor(1126,644),"9FAB") and ColorMatch(GetColor(1123,644),"99AC") and ColorMatch(GetColor(1129,644),"5E76") and ColorMatch(GetColor(1126,641),"607E") and ColorMatch(GetColor(1126,647),"1126")):
        press_key('r')
    if (ColorMatch(GetColor(1127,649),"220F") and ColorMatch(GetColor(1124,649),"FF7B") and ColorMatch(GetColor(1130,649),"2C10") and ColorMatch(GetColor(1127,646),"2C10") and ColorMatch(GetColor(1127,652),"2810")):
        press_key('f10')
    if (ColorMatch(GetColor(1125,644),"7CAD") and ColorMatch(GetColor(1122,644),"BADE") and ColorMatch(GetColor(1128,644),"B7DA") and ColorMatch(GetColor(1125,641),"82AD") and ColorMatch(GetColor(1125,647),"AFD7")):
        press_key('e')


def Mage_Arcane():
    if (ColorMatch(GetColor(659, 871), "03FF") and ColorMatch(GetColor(656, 871), "03FF") and ColorMatch(GetColor(662, 871), "03FF") and
            ColorMatch(GetColor(659, 868), "03FF") and ColorMatch(GetColor(659, 874), "03FF")):
        timerRunning = 0
        print("timerRunning = " + str(timerRunning))
        return    
    if ColorMatch(GetColor(611,861),"BF6A") and ColorMatch(GetColor(608,861),"BF6A") and ColorMatch(GetColor(614,861),"BF6A") and ColorMatch(GetColor(611,858),"BF6A") and ColorMatch(GetColor(611,864),"BF6A"):
        return    
    if (ColorMatch(GetColor(891,910),"0F36") and ColorMatch(GetColor(888,910),"1542") and ColorMatch(GetColor(894,910),"CDFB") and ColorMatch(GetColor(891,907),"A2F7") and ColorMatch(GetColor(891,913),"69E2")):
        press_key('f10')
        return
    if (ColorMatch(GetColor(889,913),"1995") and ColorMatch(GetColor(886,913),"21BE") and ColorMatch(GetColor(892,913),"1FCD") and ColorMatch(GetColor(889,910),"22B6") and ColorMatch(GetColor(889,916),"1F9D")):
        press_key('f1')
        return
    if (ColorMatch(GetColor(889,913),"1995") and ColorMatch(GetColor(886,913),"21BE") and ColorMatch(GetColor(892,913),"1FCD") and ColorMatch(GetColor(889,910),"22B6") and ColorMatch(GetColor(889,916),"1F9D")):
        press_key('f1')
    if ColorMatch(GetColor(889,910),"7BFD") and ColorMatch(GetColor(886,910),"57F7") and ColorMatch(GetColor(892,910),"89FF") and ColorMatch(GetColor(889,907),"6EFF") and ColorMatch(GetColor(889,913),"42D1"):
        press_key('f9')
    if ColorMatch(GetColor(889,914),"35AB") and ColorMatch(GetColor(886,914),"3EC5") and ColorMatch(GetColor(892,914),"6DFA") and ColorMatch(GetColor(889,911),"6EF8") and ColorMatch(GetColor(889,917),"2E99"):        
        press_key('f9')
    if (ColorMatch(GetColor(891,916),"89E3") and ColorMatch(GetColor(888,916),"EDFE") and ColorMatch(GetColor(894,916),"68D9") and ColorMatch(GetColor(891,913),"0829") and ColorMatch(GetColor(891,919),"08B5")):
        press_key('f2')
    if (ColorMatch(GetColor(888,915),"FEFF") and ColorMatch(GetColor(885,915),"F3FD") and ColorMatch(GetColor(891,915),"FBFF") and ColorMatch(GetColor(888,912),"FBFF") and ColorMatch(GetColor(888,918),"C5F0")):
        press_key('f3')
    if (ColorMatch(GetColor(892,912),"FFFF") and ColorMatch(GetColor(889,912),"FDFF") and ColorMatch(GetColor(895,912),"EBFF") and ColorMatch(GetColor(892,909),"FCFF") and ColorMatch(GetColor(892,915),"FBFF")):
        press_key('f4')
    if (ColorMatch(GetColor(890,911),"81D2") and ColorMatch(GetColor(887,911),"90CC") and ColorMatch(GetColor(893,911),"EFFC") and ColorMatch(GetColor(890,908),"6CCA") and ColorMatch(GetColor(890,914),"90D3")):
        press_key('f5')

def Druid_Balance():
    if (ColorMatch(GetColor(936,965),"9CB5") and ColorMatch(GetColor(933,965),"A6B1") and ColorMatch(GetColor(939,965),"6767") and ColorMatch(GetColor(936,962),"4151") and ColorMatch(GetColor(936,968),"7476")):
        press_key('f4')
    if (ColorMatch(GetColor(937,960),"E8F7") and ColorMatch(GetColor(934,960),"5CBC") and ColorMatch(GetColor(940,960),"56C9") and ColorMatch(GetColor(937,957),"0873") and ColorMatch(GetColor(937,963),"77CF")):
        press_key('9')
    if (ColorMatch(GetColor(937,964),"FBA5") and ColorMatch(GetColor(934,964),"B540") and ColorMatch(GetColor(940,964),"FEB9") and ColorMatch(GetColor(937,961),"FFC6") and ColorMatch(GetColor(937,967),"CD53")):
        press_key('8')
    if (ColorMatch(GetColor(937,963),"FBFF") and ColorMatch(GetColor(934,963),"FBFF") and ColorMatch(GetColor(940,963),"FBFF") and ColorMatch(GetColor(937,960),"FEFF") and ColorMatch(GetColor(937,966),"FBFF")):
        press_key('f5')
    if (ColorMatch(GetColor(938,961),"F94E") and ColorMatch(GetColor(935,961),"E707") and ColorMatch(GetColor(941,961),"F97E") and ColorMatch(GetColor(938,958),"F998") and ColorMatch(GetColor(938,964),"D502")):
        press_key('6')
    if ColorMatch(GetColor(937,962),"1632") and ColorMatch(GetColor(934,962),"FCFF") and ColorMatch(GetColor(940,962),"0B14") and ColorMatch(GetColor(937,959),"3F60") and ColorMatch(GetColor(937,965),"0305"):
        press_key('5')
    if ColorMatch(GetColor(940,958),"19FF") and ColorMatch(GetColor(937,958),"32FF") and ColorMatch(GetColor(943,958),"04F7") and ColorMatch(GetColor(940,955),"34FF") and ColorMatch(GetColor(940,961),"35FA"):
        press_key('0')
    if ColorMatch(GetColor(938,963),"E3E7") and ColorMatch(GetColor(935,963),"64AD") and ColorMatch(GetColor(941,963),"EFEF") and ColorMatch(GetColor(938,960),"F6FA") and ColorMatch(GetColor(938,966),"4163"):
        press_key('7')     
    if ColorMatch(GetColor(939,959),"FCFF") and ColorMatch(GetColor(936,959),"F1FA") and ColorMatch(GetColor(942,959),"E6F7") and ColorMatch(GetColor(939,956),"748C") and ColorMatch(GetColor(939,962),"FFFF"):
        press_key('f3')           

def Hunter_Marksmanship():
    if (ColorMatch(GetColor(659, 871), "03FF") and ColorMatch(GetColor(656, 871), "03FF") and ColorMatch(GetColor(662, 871), "03FF") and
            ColorMatch(GetColor(659, 868), "03FF") and ColorMatch(GetColor(659, 874), "03FF")):
        timerRunning = 0
        print("timerRunning = " + str(timerRunning))
        return
    if ColorMatch(GetColor(937,961),"7039") and ColorMatch(GetColor(934,961),"524E") and ColorMatch(GetColor(940,961),"AB49") and ColorMatch(GetColor(937,958),"6163") and ColorMatch(GetColor(937,964),"B658"):
        press_key('f1')
    if ColorMatch(GetColor(936,964),"4B40") and ColorMatch(GetColor(933,964),"4625") and ColorMatch(GetColor(939,964),"0408") and ColorMatch(GetColor(936,961),"1821") and ColorMatch(GetColor(936,967),"4044"):
        press_key('f3')
    if ColorMatch(GetColor(934,964),"2D24") and ColorMatch(GetColor(931,964),"5642") and ColorMatch(GetColor(937,964),"5C2C") and ColorMatch(GetColor(934,961),"160F") and ColorMatch(GetColor(934,967),"6033"):
        press_key('f2')
    if ColorMatch(GetColor(938,966),"6743") and ColorMatch(GetColor(935,966),"3626") and ColorMatch(GetColor(941,966),"302B") and ColorMatch(GetColor(938,963),"0000") and ColorMatch(GetColor(938,969),"7155"):
        press_key('f4')
    if ColorMatch(GetColor(935,963),"E8E9") and ColorMatch(GetColor(932,963),"9AA7") and ColorMatch(GetColor(938,963),"6573") and ColorMatch(GetColor(935,960),"7C8E") and ColorMatch(GetColor(935,966),"8695"):
        press_key('f5')
    if ColorMatch(GetColor(938,963),"77AA") and ColorMatch(GetColor(935,963),"4028") and ColorMatch(GetColor(941,963),"5F89") and ColorMatch(GetColor(938,960),"9F36") and ColorMatch(GetColor(938,966),"322D"):
        press_key('f6')
    if ColorMatch(GetColor(940,960),"0400") and ColorMatch(GetColor(937,960),"4620") and ColorMatch(GetColor(943,960),"9686") and ColorMatch(GetColor(940,957),"4A3D") and ColorMatch(GetColor(940,963),"0910"):
        press_key('f7')
    if ColorMatch(GetColor(934,961),"524E") and ColorMatch(GetColor(931,961),"AAB9") and ColorMatch(GetColor(937,961),"7039") and ColorMatch(GetColor(934,958),"95A7") and ColorMatch(GetColor(934,964),"8863"):
        press_key('f1')
    if ColorMatch(GetColor(939,968),"8F9A") and ColorMatch(GetColor(936,968),"7D4E") and ColorMatch(GetColor(942,968),"6391") and ColorMatch(GetColor(939,965),"8081") and ColorMatch(GetColor(939,971),"6974"):
        press_key('f1')


        
def Paladin_Retribution():
    global timerRunning, key_2, key_3, key_q, key_f1, key_f
    if key_2 == 1:
        if ((ColorMatch(GetColor(530, 949), "DB8C") and ColorMatch(GetColor(527, 949), "7E2F") and ColorMatch(GetColor(533, 949), "4C11") and
                ColorMatch(GetColor(530, 946), "BA85") and ColorMatch(GetColor(530, 952), "6128"))):
            press_key('2')
            return
        else:
            key_2 = 0
            print("key_2 = " + str(key_2))
    if key_3 == 1:
        if ((ColorMatch(GetColor(575, 949), "7D68") and ColorMatch(GetColor(572, 949), "767B") and ColorMatch(GetColor(578, 949), "C98E") and
                ColorMatch(GetColor(575, 946), "4754") and ColorMatch(GetColor(575, 952), "4552"))):
            press_key('3')
            return
        else:
            key_3 = 0
            print("key_3 = " + str(key_3))
    if key_q == 1:
        if ((ColorMatch(GetColor(561, 949), "FFFF") and ColorMatch(GetColor(558, 949), "FFFF") and ColorMatch(GetColor(564, 949), "D508") and
                ColorMatch(GetColor(561, 946), "BD10") and ColorMatch(GetColor(561, 952), "DC2E"))):
            press_key('q')
            return
        else:
            key_q = 0
            print("key_q = " + str(key_q))
    if key_f1 == 1:
        if ((ColorMatch(GetColor(546, 950), "542A") and ColorMatch(GetColor(543, 950), "5429") and ColorMatch(GetColor(549, 950), "829F") and
                ColorMatch(GetColor(546, 947), "C91F") and ColorMatch(GetColor(546, 953), "8C6D"))):
            press_key('f1')
            return
        else:
            key_f1 = 0
            print("key_f1 = " + str(key_f1))
    if key_f == 1:
        if (ColorMatch(GetColor(591,949),"B3AD") and ColorMatch(GetColor(588,949),"B951") and ColorMatch(GetColor(594,949),"B2B1") and ColorMatch(GetColor(591,946),"CDCC") and ColorMatch(GetColor(591,952),"6331")):
            press_key('f')
        else:
            key_f = 0
            print("key_f = " + str(key_f))
    if (ColorMatch(GetColor(659, 871), "03FF") and ColorMatch(GetColor(656, 871), "03FF") and ColorMatch(GetColor(662, 871), "03FF") and
            ColorMatch(GetColor(659, 868), "03FF") and ColorMatch(GetColor(659, 874), "03FF")):
        timerRunning = 0
        print("timerRunning = " + str(timerRunning))
        return
    if (ColorMatch(GetColor(939,914),"8E1D") and ColorMatch(GetColor(936,914),"5111") and ColorMatch(GetColor(942,914),"4908") and ColorMatch(GetColor(939,911),"841E") and ColorMatch(GetColor(939,917),"1606")):
        press_key('f10')
    if (ColorMatch(GetColor(938,908),"FF8C") and ColorMatch(GetColor(935,908),"F773") and ColorMatch(GetColor(941,908),"A734") and ColorMatch(GetColor(938,905),"C23A") and ColorMatch(GetColor(938,911),"F245")):
        press_key('6')
    if (ColorMatch(GetColor(939,909),"B256") and ColorMatch(GetColor(936,909),"8F76") and ColorMatch(GetColor(942,909),"FC9E") and ColorMatch(GetColor(939,906),"2F32") and ColorMatch(GetColor(939,912),"F3AE")):
        press_key('f6')
    if (ColorMatch(GetColor(936,914),"2D0B") and ColorMatch(GetColor(933,914),"0D08") and ColorMatch(GetColor(939,914),"3D0A") and ColorMatch(GetColor(936,911),"130F") and ColorMatch(GetColor(936,917),"8911")):
        press_key('5')
    if (ColorMatch(GetColor(937,916),"8145") and ColorMatch(GetColor(934,916),"824A") and ColorMatch(GetColor(940,916),"854E") and ColorMatch(GetColor(937,913),"4711") and ColorMatch(GetColor(937,919),"3D23")):
        press_key('f3')
    if (ColorMatch(GetColor(939,913),"633B") and ColorMatch(GetColor(936,913),"7157") and ColorMatch(GetColor(942,913),"B7B5") and ColorMatch(GetColor(939,910),"7152") and ColorMatch(GetColor(939,916),"8C74")):
        press_key('f')
    if (ColorMatch(GetColor(939,913),"FAFC") and ColorMatch(GetColor(936,913),"4805") and ColorMatch(GetColor(942,913),"D4BD") and ColorMatch(GetColor(939,910),"CDC4") and ColorMatch(GetColor(939,916),"4A01")):
        press_key('r')
    if (ColorMatch(GetColor(939,914),"444D") and ColorMatch(GetColor(936,914),"6A72") and ColorMatch(GetColor(942,914),"4551") and ColorMatch(GetColor(939,911),"3843") and ColorMatch(GetColor(939,917),"4150")):
        press_key('f2')
    if (ColorMatch(GetColor(935,912),"2215") and ColorMatch(GetColor(932,912),"2E21") and ColorMatch(GetColor(938,912),"3420") and ColorMatch(GetColor(935,909),"442A") and ColorMatch(GetColor(935,915),"1F13")):
        press_key('f2')
    if (ColorMatch(GetColor(935,915),"625D") and ColorMatch(GetColor(932,915),"4944") and ColorMatch(GetColor(938,915),"8780") and ColorMatch(GetColor(935,912),"C0B2") and ColorMatch(GetColor(935,918),"2723")):
        press_key('f11')
    if (ColorMatch(GetColor(937,916),"420B") and ColorMatch(GetColor(934,916),"C916") and ColorMatch(GetColor(940,916),"1D13") and ColorMatch(GetColor(937,913),"2F0C") and ColorMatch(GetColor(937,919),"1E12")):
        press_key('5')        
    if (ColorMatch(GetColor(1141, 620), "0400") and ColorMatch(GetColor(1138, 620), "7605") and ColorMatch(GetColor(1144, 620), "7F14") and
            ColorMatch(GetColor(1141, 617), "830A") and ColorMatch(GetColor(1141, 623), "9117")):
        press_key('f1')
        return
    if (ColorMatch(GetColor(1141, 623), "2505") and ColorMatch(GetColor(1138, 623), "0901") and ColorMatch(GetColor(1144, 623), "360C") and
            ColorMatch(GetColor(1141, 620), "1C05") and ColorMatch(GetColor(1141, 626), "1802")):
        press_key('e')
        return
    if (ColorMatch(GetColor(1139, 621), "7937") and ColorMatch(GetColor(1136, 621), "C87F") and ColorMatch(GetColor(1142, 621), "A252") and
            ColorMatch(GetColor(1139, 618), "8039") and ColorMatch(GetColor(1139, 624), "6E2E")):
        press_key('7')
        return
    if (ColorMatch(GetColor(1130, 621), "2800") and ColorMatch(GetColor(1127, 621), "1000") and ColorMatch(GetColor(1133, 621), "4E06") and
            ColorMatch(GetColor(1130, 618), "4A10") and ColorMatch(GetColor(1130, 624), "2C00")):
        press_key('x')
        return
    if (ColorMatch(GetColor(1142, 625), "F3CE") and ColorMatch(GetColor(1139, 625), "EDCE") and ColorMatch(GetColor(1145, 625), "C3AC") and
            ColorMatch(GetColor(1142, 622), "F1D2") and ColorMatch(GetColor(1142, 628), "9D8F")):
        press_key('f4')
        return
    if (ColorMatch(GetColor(1138, 611), "8808") and ColorMatch(GetColor(1135, 611), "AE03") and ColorMatch(GetColor(1141, 611), "5908") and
            ColorMatch(GetColor(1138, 608), "D210") and ColorMatch(GetColor(1138, 614), "7E08")):
        press_key('f12')
        return
    if (ColorMatch(GetColor(1140, 620), "510D") and ColorMatch(GetColor(1137, 620), "A82D") and ColorMatch(GetColor(1143, 620), "C331") and
            ColorMatch(GetColor(1140, 617), "A84B") and ColorMatch(GetColor(1140, 623), "620D")):
        press_key('g')
        return
    if (ColorMatch(GetColor(1142,622),"7F3D") and ColorMatch(GetColor(1139,622),"2210") and ColorMatch(GetColor(1145,622),"0C08") and ColorMatch(GetColor(1142,619),"2011") and ColorMatch(GetColor(1142,625),"5129")):
        press_key('9')
        return
    if (ColorMatch(GetColor(1140, 620), "FBFF") and ColorMatch(GetColor(1137, 620), "C5B5") and ColorMatch(GetColor(1143, 620), "AE9F") and
            ColorMatch(GetColor(1140, 617), "BAAA") and ColorMatch(GetColor(1140, 623), "D8C8")):
        press_key('r')
        return
    if (ColorMatch(GetColor(1140, 627), "1810") and ColorMatch(GetColor(1137, 627), "2014") and ColorMatch(GetColor(1143, 627), "1810") and
            ColorMatch(GetColor(1140, 624), "1C0C") and ColorMatch(GetColor(1140, 630), "4507")):
        press_key('f2')
        return
    if (ColorMatch(GetColor(1135,613),"7529") and ColorMatch(GetColor(1132,613),"A23B") and ColorMatch(GetColor(1138,613),"825A") and ColorMatch(GetColor(1135,610),"7727") and ColorMatch(GetColor(1135,616),"4D18")):
        press_key('f')
        return
    if (ColorMatch(GetColor(1138, 624), "6D66") and ColorMatch(GetColor(1135, 624), "8E84") and ColorMatch(GetColor(1141, 624), "6D66") and
            ColorMatch(GetColor(1138, 621), "4847") and ColorMatch(GetColor(1138, 627), "5B5D")):
        press_key('f11')
        return
    if (ColorMatch(GetColor(1141, 624), "8F15") and ColorMatch(GetColor(1138, 624), "1A0A") and ColorMatch(GetColor(1144, 624), "1008") and
            ColorMatch(GetColor(1141, 621), "210A") and ColorMatch(GetColor(1141, 627), "160E")):
        press_key('5')
        return
    if (ColorMatch(GetColor(1139, 622), "D889") and ColorMatch(GetColor(1136, 622), "6C1B") and ColorMatch(GetColor(1142, 622), "A64A") and
            ColorMatch(GetColor(1139, 619), "EBA5") and ColorMatch(GetColor(1139, 625), "D486")):
        press_key('2')
        return
    if (ColorMatch(GetColor(1142, 617), "DFD6") and ColorMatch(GetColor(1139, 617), "C0B5") and ColorMatch(GetColor(1145, 617), "744C") and
            ColorMatch(GetColor(1142, 614), "CBBC") and ColorMatch(GetColor(1142, 620), "9F60")):
        press_key('t')
        return
    if (ColorMatch(GetColor(1140, 623), "CB4A") and ColorMatch(GetColor(1137, 623), "B259") and ColorMatch(GetColor(1143, 623), "DB76") and
            ColorMatch(GetColor(1140, 620), "CD4A") and ColorMatch(GetColor(1140, 626), "C239")):
        press_key('t')
        return
    if (ColorMatch(GetColor(1141, 623), "6D46") and ColorMatch(GetColor(1138, 623), "6D46") and ColorMatch(GetColor(1144, 623), "8F6E") and
            ColorMatch(GetColor(1141, 620), "7942") and ColorMatch(GetColor(1141, 626), "2014")):
        press_key('f3')
        return
    if (ColorMatch(GetColor(1139, 628), "A340") and ColorMatch(GetColor(1136, 628), "782A") and ColorMatch(GetColor(1142, 628), "D352") and
            ColorMatch(GetColor(1139, 625), "E75A") and ColorMatch(GetColor(1139, 631), "B041")):
        press_key('6')
        return
    if (ColorMatch(GetColor(1141, 619), "FFBD") and ColorMatch(GetColor(1138, 619), "B67B") and ColorMatch(GetColor(1144, 619), "F794") and
            ColorMatch(GetColor(1141, 616), "5E54") and ColorMatch(GetColor(1141, 622), "FDAC")):
        press_key('f6')
        return
    if (ColorMatch(GetColor(1141, 622), "9C7F") and ColorMatch(GetColor(1138, 622), "D9B1") and ColorMatch(GetColor(1144, 622), "A681") and
            ColorMatch(GetColor(1141, 619), "DFBD") and ColorMatch(GetColor(1141, 625), "B287")):
        press_key('f5')
        return
    if (ColorMatch(GetColor(1141, 620), "C634") and ColorMatch(GetColor(1138, 620), "4908") and ColorMatch(GetColor(1144, 620), "0000") and
            ColorMatch(GetColor(1141, 617), "FF8C") and ColorMatch(GetColor(1141, 623), "FF6B")):
        press_key('6')
        return



def on_click(x, y, button, pressed):
    # 检查是否是中键被按下
    global timerRunning
    if button == mouse.Button.middle and pressed:
        if timerRunning == 0:
            timerRunning = 1
        else:
            timerRunning = 0
        print("timerRunning = " + str(timerRunning))
    
    # 在调试模式下，左键点击时采样颜色
    if debug_mode and button == mouse.Button.left and pressed:
        sample_x, sample_y = mouse_controller.position
        color = GetColor(sample_x, sample_y)
        print(f"样本位置 ({sample_x}, {sample_y}) 的颜色: {color}")
        # 生成条件检测代码
        print(f'if (GetColor({sample_x}, {sample_y})=="{color}") and (GetColor({sample_x-3}, {sample_y})=="{GetColor(sample_x-3, sample_y)}") and (GetColor({sample_x+3}, {sample_y})=="{GetColor(sample_x+3, sample_y)}") and (GetColor({sample_x}, {sample_y-3})=="{GetColor(sample_x, sample_y-3)}") and (GetColor({sample_x}, {sample_y+3})=="{GetColor(sample_x, sample_y+3)}")):')
        print('    press_key(\'键位\')')


def on_press(key):
    global key_2, key_3, key_q, key_f1, timerRunning, debug_mode, key_f
    try:
        key = repr(key)
        key = key.replace('Key.', '')
        key = key.replace("'", '')
        key = key.replace("<", '')
        key = key.split(':', 1)[0]
        
        # 添加F12键切换调试模式
        #if key == 'f12':
        #    debug_mode = not debug_mode
        #    print(f"调试模式: {'开启' if debug_mode else '关闭'}")
            
        if timerRunning == 1:
            if key == '2':
                key_2 = 1
                print("key_2 = " + str(key_2))
            if key == '3':
                key_3 = 1
                print("key_3 = " + str(key_3))
            if key == 'q':
                key_q = 1
                print("key_q = " + str(key_q))
            if key == 'f1':
                key_f1 = 1
                print("key_f1 = " + str(key_f1))
            if key == 'f':
                key_f = 1
                print("key_f = " + str(key_f))                
    except AttributeError:
        print('error')


# 在程序开始时调用修复函数
if __name__ == "__main__":
    listener = keyboard.Listener(
        on_press=on_press)
    listener.start()

    # 创建监听器
    listener = mouse.Listener(
        on_click=on_click)
    listener.start()

    while True:
        # 生成5ms-10ms的随机延迟
        random_delay = random.uniform(0.005, 0.010)  # 5ms到10ms
        time.sleep(random_delay)
        label.update()
        
        # 调试模式下显示鼠标位置
        if debug_mode:
            mouse_pos = mouse_controller.position
            label.config(text=f"Debug: {mouse_pos[0]},{mouse_pos[1]}", fg='green')
        elif timerRunning == 1:
            label.config(text="Running", fg='red')
            # 在调试模式下，添加额外的调试点
            if debug_mode:
                # 检测特定位置的颜色作为参考
                debug_color = GetColor(100, 100)
                print(f"参考点 (100, 100) 颜色: {debug_color}")
            
            # 原有的检测逻辑
            if (ColorMatch(GetColor(454, 842), "7588") and ColorMatch(GetColor(451, 842), "4A7E") and ColorMatch(GetColor(457, 842), "CFEB") and
                    ColorMatch(GetColor(454, 839), "6E83") and ColorMatch(GetColor(454, 845), "1E37")):
                Paladin_Retribution()
            if (ColorMatch(GetColor(455, 844), "2DDC") and ColorMatch(GetColor(452, 844), "067D") and ColorMatch(GetColor(458, 844), "0E89") and
                    ColorMatch(GetColor(455, 841), "86B7") and ColorMatch(GetColor(455, 847), "0A30")):
                Shaman_Elemental()
            if (ColorMatch(GetColor(455, 843), "6F33") and ColorMatch(GetColor(452, 843), "140C") and ColorMatch(GetColor(458, 843), "0300") and
                    ColorMatch(GetColor(455, 840), "9355") and ColorMatch(GetColor(455, 846), "A71D")):
                Shaman_Restoration()
            if (ColorMatch(GetColor(453, 843), "5070") and ColorMatch(GetColor(450, 843), "6F7E") and ColorMatch(GetColor(456, 843), "8F59") and
                    ColorMatch(GetColor(453, 840), "715A") and ColorMatch(GetColor(453, 846), "9357")):
                Rogue_Outlaw()
            if (ColorMatch(GetColor(454, 843), "AD75") and ColorMatch(GetColor(451, 843), "3B22") and ColorMatch(GetColor(457, 843), "BF6F") and
                    ColorMatch(GetColor(454, 840), "BD6B") and ColorMatch(GetColor(454, 846), "FBB5")):
                Deathknight_Unholy()
            if (ColorMatch(GetColor(454, 844), "C0BE") and ColorMatch(GetColor(451, 844), "C290") and
                    ColorMatch(GetColor(457, 844), "0605") and ColorMatch(GetColor(454, 841), "0503") and ColorMatch(GetColor(454, 847), "1008")):
                Rogue_Assasination()
            if(ColorMatch(GetColor(454, 844), "6824") and ColorMatch(GetColor(451, 844), "539C") and ColorMatch(GetColor(457, 844), "DD98") and
                        ColorMatch(GetColor(454, 841), "D7FF") and ColorMatch(GetColor(454, 847), "1D05")):
                Shaman_Enhancement()
            if (ColorMatch(GetColor(454, 844), "E3DF") and ColorMatch(GetColor(451, 844), "EDEB") and ColorMatch(GetColor(457, 844), "6E52") and
                    ColorMatch(GetColor(454, 841), "F5F2") and ColorMatch(GetColor(454, 847), "CDD2")):
                Priest_Discipline()
            if (ColorMatch(GetColor(454,844),"9FE3") and ColorMatch(GetColor(451,844),"1C8C") and ColorMatch(GetColor(457,844),"1466") and ColorMatch(GetColor(454,841),"043F") and ColorMatch(GetColor(454,847),"CFE7")):
                Mage_Arcane()
            if (ColorMatch(GetColor(454,844),"FDFF") and ColorMatch(GetColor(451,844),"9CCD") and ColorMatch(GetColor(457,844),"3095") and ColorMatch(GetColor(454,841),"4475") and ColorMatch(GetColor(454,847),"F7E4")):
                Druid_Balance()
            if ColorMatch(GetColor(455,844),"1F1D") and ColorMatch(GetColor(452,844),"4A28") and ColorMatch(GetColor(458,844),"4E35") and ColorMatch(GetColor(455,841),"C0A2") and ColorMatch(GetColor(455,847),"251B"):
                Hunter_Marksmanship()

             
        else:
            label.config(text="Holding", fg='blue')
