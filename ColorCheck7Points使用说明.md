# ColorCheck7Points函数使用说明

## 功能概述

`ColorCheck7Points` 是一个新增的颜色检测函数，它可以检查7个点的颜色匹配情况，当其中任意指定数量的点满足ColorMatch时就返回True。这比原来的ColorCheck函数更加灵活和容错。

## 函数签名

```python
def ColorCheck7Points(center_x, center_y, ref, min_matches=4):
```

## 参数说明

- `center_x, center_y`: 中心点坐标
- `ref`: 期望的颜色值（4位十六进制字符串，如"FBFF"）
- `min_matches`: 最少需要匹配的点数量，默认为4

## 检测点位置

函数会检查以下7个点的颜色：

1. `(center_x, center_y)` - 中心点
2. `(center_x, center_y - 3)` - 上方3像素
3. `(center_x, center_y + 3)` - 下方3像素
4. `(center_x - 3, center_y)` - 左侧3像素
5. `(center_x + 3, center_y)` - 右侧3像素
6. `(center_x - 3, center_y - 3)` - 左上角3像素
7. `(center_x + 3, center_y + 3)` - 右下角3像素

## 返回值

- `True`: 如果匹配的点数量 >= min_matches
- `False`: 如果匹配的点数量 < min_matches

## 使用示例

### 基本使用

```python
# 检查位置(1140, 620)，期望颜色为"FBFF"，需要至少4个点匹配
if ColorCheck7Points(1140, 620, "FBFF", min_matches=4):
    press_key('f9')
```

### 不同匹配要求

```python
# 严格模式：需要所有7个点都匹配
if ColorCheck7Points(1140, 620, "FBFF", min_matches=7):
    press_key('f9')

# 宽松模式：只需要3个点匹配
if ColorCheck7Points(1140, 620, "FBFF", min_matches=3):
    press_key('f9')

# 默认模式：需要4个点匹配（推荐）
if ColorCheck7Points(1140, 620, "FBFF"):
    press_key('f9')
```

### 在游戏检测中的应用

```python
def Priest_Discipline_Enhanced():
    """
    使用新的ColorCheck7Points函数的增强版牧师戒律检测
    """
    # 技能1：7个点中任意4个匹配就触发
    if ColorCheck7Points(1135, 623, "E773", min_matches=4):
        press_key('f11')
        return
    
    # 技能2：更严格的要求，需要5个点匹配
    if ColorCheck7Points(1140, 621, "FBFF", min_matches=5):
        press_key('f9')
        return
    
    # 技能3：宽松要求，只需要3个点匹配
    if ColorCheck7Points(1141, 622, "9C7F", min_matches=3):
        press_key('f12')
        return
```

## 优势对比

### 原来的ColorCheck函数
```python
def ColorCheck(x, y, ref):
    if (ColorMatch(GetColor(x, y), ref) and 
        ColorMatch(GetColor(x - 3, y), ref) and 
        ColorMatch(GetColor(x + 3, y), ref) and
        ColorMatch(GetColor(x, y - 3), ref) and 
        ColorMatch(GetColor(x, y + 3), ref)):
        return [x, y]
    else:
        return False
```

**限制：**
- 只检查5个点
- 必须所有5个点都匹配才返回True
- 不够灵活，容易因为单个像素差异而失败

### 新的ColorCheck7Points函数

**优势：**
- 检查7个点，覆盖更全面
- 可以自定义最少匹配数量
- 更加容错，提高检测成功率
- 保持足够的精确度

## 推荐设置

根据不同场景推荐的min_matches值：

- **一般技能检测**: `min_matches=4` (默认值)
- **重要技能检测**: `min_matches=5` (更严格)
- **不稳定环境**: `min_matches=3` (更宽松)
- **精确检测**: `min_matches=6` 或 `7` (最严格)

## 性能优化

函数包含早期返回优化：
```python
if match_count >= min_matches:
    return True  # 一旦达到要求就立即返回，不继续检查剩余点
```

这意味着在大多数情况下，函数不需要检查所有7个点就能得出结果。

## 调试功能

测试文件 `test_7points_color_check.py` 包含详细的调试输出，显示：
- 每个点的坐标和实际颜色
- 匹配和不匹配的点列表
- 总匹配数量

## 兼容性

- 完全兼容现有的ColorMatch函数
- 可以与原有的ColorCheck函数并存
- 不影响现有代码的功能

## 使用建议

1. **逐步替换**: 可以先在新功能中使用，验证效果后再替换现有代码
2. **测试调优**: 根据实际游戏环境调整min_matches参数
3. **备用方案**: 可以同时保留原有检测方法作为备用
4. **性能考虑**: 虽然检查点更多，但早期返回机制保证了良好的性能

## 注意事项

- 确保中心点坐标准确
- 根据实际需要调整min_matches值
- 在不同显示器和游戏设置下测试效果
- 可以结合原有的ColorCheck函数使用，提供多层检测
