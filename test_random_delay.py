#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import random
import time

def test_random_delay():
    """测试5ms-10ms随机延迟功能"""
    print("测试5ms-10ms随机延迟功能")
    print("=" * 40)
    
    delays = []
    test_count = 10
    
    print(f"进行 {test_count} 次延迟测试...")
    
    for i in range(test_count):
        # 生成5ms-10ms的随机延迟
        random_delay = random.uniform(0.005, 0.010)  # 5ms到10ms
        delays.append(random_delay)
        
        print(f"第 {i+1:2d} 次: {random_delay*1000:.2f}ms")
        
        # 实际执行延迟（为了演示，这里不真的sleep）
        # time.sleep(random_delay)
    
    print("\n统计信息:")
    print(f"最小延迟: {min(delays)*1000:.2f}ms")
    print(f"最大延迟: {max(delays)*1000:.2f}ms")
    print(f"平均延迟: {sum(delays)/len(delays)*1000:.2f}ms")
    
    # 验证所有延迟都在5ms-10ms范围内
    all_in_range = all(0.005 <= delay <= 0.010 for delay in delays)
    print(f"所有延迟都在5ms-10ms范围内: {'✓' if all_in_range else '✗'}")

def test_actual_timing():
    """测试实际的延迟时间精度"""
    print("\n" + "=" * 40)
    print("测试实际延迟时间精度")
    print("=" * 40)
    
    test_count = 5
    print(f"进行 {test_count} 次实际延迟测试...")
    
    for i in range(test_count):
        random_delay = random.uniform(0.005, 0.010)
        expected_ms = random_delay * 1000
        
        start_time = time.time()
        time.sleep(random_delay)
        actual_time = time.time() - start_time
        actual_ms = actual_time * 1000
        
        print(f"第 {i+1} 次: 期望 {expected_ms:.2f}ms, 实际 {actual_ms:.2f}ms, 差异 {abs(actual_ms-expected_ms):.2f}ms")

if __name__ == "__main__":
    test_random_delay()
    test_actual_timing()
