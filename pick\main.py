import tkinter as tk
from tkinter import simpledialog, messagebox, filedialog
from PIL import ImageGrab
from pynput.mouse import Controller as Mouse<PERSON>ontroller

class ColorPickerGUI:
    def __init__(self, master):
        self.master = master
        self.master.title("取色")
        
        self.mouse = MouseController()

        self.label = tk.Label(master, text="鼠标移动到指定位置然后按 Enter.")
        self.label.pack()

        self.pick_color_btn = tk.But<PERSON>(master, text="取色（按Enter）", command=self.pick_color_and_bind_key)
        self.pick_color_btn.pack()

        # 给按钮设置焦点以便可以通过键盘事件触发
        self.pick_color_btn.focus_set()

        # 绑定回车键到pick_color函数
        self.master.bind('<Return>', lambda event: self.pick_color_and_bind_key())

        # 新增按钮：自动获取(940,915)的9个点
        self.auto_pick_btn = tk.But<PERSON>(master, text="自动获取(940,915)九点", command=self.auto_pick_nine_points)
        self.auto_pick_btn.pack()

        self.close_btn = tk.<PERSON><PERSON>(master, text="完成并生成", command=self.finish_and_generate_script)
        self.close_btn.pack()

        self.color_bindings = []

    def pick_color_and_bind_key(self):
        # Capture the color at the current mouse position and its surrounding pixels
        x, y = self.mouse.position
        colors = self.get_surrounding_colors(x, y)
        # Ask for the key to bind with these colors
        key = simpledialog.askstring("按键绑定", "输入想要绑定的按键:", parent=self.master)
        if key:
            self.color_bindings.append({"colors": colors, "key": key})
            messagebox.showinfo("Color Picked and Key Bound", f"Colors at ({x}, {y}) and surrounding bound to key: {key}")

    def get_surrounding_colors(self, x, y):
        offsets = [(0, 0), (-3, 0), (3, 0), (0, -3), (0, 3)]
        colors = []

        # 捕获屏幕截图
        screenshot = ImageGrab.grab()

        for dx, dy in offsets:
            px = x + dx
            py = y + dy
            try:
                color = screenshot.getpixel((px, py))
                # 如果返回的是RGB元组，转换为十六进制
                if isinstance(color, tuple):
                    r, g, b = color[:3]  # 取前三个值 (RGB)
                    color_hex = f"{r:02X}{g:02X}{b:02X}"[-4:]  # 只取后4位
                else:
                    color_hex = f"{color:06X}"[-4:]  # 如果是整数，转换为十六进制后取后4位
                colors.append({"x": px, "y": py, "color": color_hex})
            except IndexError:
                # 如果坐标超出屏幕范围，跳过这个点位
                continue

        return colors

    def get_nine_points_colors(self, center_x, center_y):
        """
        获取9个点的颜色：中心点 + 上下左右各3像素 + 四个对角线各3像素
        """
        # 定义9个检查点：中心点 + 上下左右 + 四个对角线
        offsets = [
            (0, 0),      # 中心点
            (0, -3),     # 上
            (0, 3),      # 下
            (-3, 0),     # 左
            (3, 0),      # 右
            (-3, -3),    # 左上
            (3, -3),     # 右上
            (-3, 3),     # 左下
            (3, 3),      # 右下
        ]

        colors = []

        # 捕获屏幕截图
        screenshot = ImageGrab.grab()

        for dx, dy in offsets:
            px = center_x + dx
            py = center_y + dy
            try:
                color = screenshot.getpixel((px, py))
                # 如果返回的是RGB元组，转换为十六进制
                if isinstance(color, tuple):
                    r, g, b = color[:3]  # 取前三个值 (RGB)
                    color_hex = f"{r:02X}{g:02X}{b:02X}"[-4:]  # 只取后4位
                else:
                    color_hex = f"{color:06X}"[-4:]  # 如果是整数，转换为十六进制后取后4位
                colors.append({"x": px, "y": py, "color": color_hex})
            except IndexError:
                # 如果坐标超出屏幕范围，跳过这个点位
                continue

        return colors

    def auto_pick_nine_points(self):
        """
        自动获取坐标(940,915)的9个点的颜色值
        """
        center_x, center_y = 940, 915
        colors = self.get_nine_points_colors(center_x, center_y)

        # 询问要绑定的按键
        key = simpledialog.askstring("按键绑定", "输入想要绑定的按键:", parent=self.master)
        if key:
            # 使用特殊标记表示这是9点检测模式
            self.color_bindings.append({
                "colors": colors,
                "key": key,
                "mode": "nine_points",
                "center_x": center_x,
                "center_y": center_y
            })
            messagebox.showinfo("九点颜色获取完成",
                              f"已获取坐标({center_x}, {center_y})周围9个点的颜色并绑定到按键: {key}\n"
                              f"获取到 {len(colors)} 个有效点位")

    def finish_and_generate_script(self):
        filename = filedialog.asksaveasfilename(defaultextension=".ahk", filetypes=[("Scripts", "*.test")], parent=self.master)
        if filename:
            with open(filename, 'w') as file:
                file.write(";\n#NoEnv\n#Warn\nSendMode Input\nSetWorkingDir %A_ScriptDir%\n\nXButton1::\nSetTimer,CheckColors, 0\nKeyWait, XButton1\nSetTimer,CheckColors, Off\nReturn\n\nCheckColors:\n")

                for binding in self.color_bindings:
                    # 检查是否是9点检测模式
                    if binding.get("mode") == "nine_points":
                        # 直接调用ColorCheck9Points，传入9个点的像素值
                        center_x = binding["center_x"]
                        center_y = binding["center_y"]
                        # 构建9个点的像素值字符串
                        pixel_values = []
                        for color_info in binding["colors"]:
                            pixel_values.append(f'"{color_info["color"]}"')
                        pixel_values_str = ", ".join(pixel_values)
                        file.write(f"    if ColorCheck9Points({center_x}, {center_y}, {pixel_values_str}, 5):\n")
                        file.write(f"            press_key('{binding['key']}')\n    ")
                    else:
                        # 原有的多点检测模式
                        conditions = " and ".join([f'ColorMatch(GetColor({color["x"]},{color["y"]}),"{color["color"]}")' for color in binding["colors"]])
                        file.write(f"    if {conditions}:\n")
                        file.write(f"            press_key('{binding['key']}')\n    ")

                file.write("Return\n\nGetColor(x,y)\n{\n    CoordMode, Pixel, Screen\n    PixelGetColor, color, x, y, RGB\n    StringRight, color, color, 4\n    return color\n}\n")
            messagebox.showinfo("Success", "AHK script generated successfully!")

if __name__ == "__main__":
    root = tk.Tk()
    my_gui = ColorPickerGUI(root)
    root.mainloop()
