#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ColorCheck7Points函数的示例文件
演示如何使用7个点中任意4个满足ColorMatch就返回True的功能
"""

import sys
import os

# 添加当前目录到Python路径，以便导入主文件中的函数
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 模拟GetColor和ColorMatch函数（用于测试）
def GetColor(x, y):
    """
    模拟GetColor函数，返回固定的颜色值用于测试
    在实际使用中，这个函数会从屏幕获取像素颜色
    """
    # 模拟一些测试数据
    test_colors = {
        (1140, 620): "FBFF",    # 中心点
        (1140, 617): "FBFF",    # 上
        (1140, 623): "FBFF",    # 下
        (1137, 620): "FBFF",    # 左
        (1143, 620): "FFFF",    # 右 - 稍微不同
        (1137, 617): "FBFF",    # 左上
        (1143, 623): "FBFF",    # 右下
    }
    
    return test_colors.get((x, y), "0000")  # 默认返回黑色


def ColorMatch(actual_color, expected_color, tolerance=5):
    """
    比较两个颜色是否在指定的误差范围内匹配
    actual_color: 实际获取的颜色 (4位十六进制字符串)
    expected_color: 期望的颜色 (4位十六进制字符串)
    tolerance: 允许的误差值 (默认为5)
    """
    if actual_color == "ERROR" or expected_color == "ERROR":
        return False

    try:
        # 将4位十六进制颜色转换为两个字节的值
        def hex_to_bytes(hex_color):
            # 确保是4位十六进制
            if len(hex_color) == 4:
                # 将4位十六进制分解为两个字节
                byte1 = int(hex_color[0:2], 16)  # 前两位
                byte2 = int(hex_color[2:4], 16)  # 后两位
                return (byte1, byte2)
            else:
                return (0, 0)

        actual_bytes = hex_to_bytes(actual_color)
        expected_bytes = hex_to_bytes(expected_color)

        # 检查每个字节是否在误差范围内
        for i in range(2):
            if abs(actual_bytes[i] - expected_bytes[i]) > tolerance:
                return False

        return True
    except Exception as e:
        print(f"颜色比较错误: {e}")
        return False


def ColorCheck7Points(center_x, center_y, ref, min_matches=4):
    """
    检查7个点的颜色匹配情况：中心点 + 上下左右各3像素 + 对角线各3像素
    如果7个点中有任意4个（或指定数量）满足ColorMatch就返回True
    
    参数:
    center_x, center_y: 中心点坐标
    ref: 期望的颜色值
    min_matches: 最少需要匹配的点数量，默认为4
    
    返回:
    如果匹配的点数量 >= min_matches，返回True，否则返回False
    """
    # 定义7个检查点：中心点 + 上下左右 + 左上右下对角线
    points = [
        (center_x, center_y),           # 中心点
        (center_x, center_y - 3),       # 上
        (center_x, center_y + 3),       # 下
        (center_x - 3, center_y),       # 左
        (center_x + 3, center_y),       # 右
        (center_x - 3, center_y - 3),   # 左上
        (center_x + 3, center_y + 3),   # 右下
    ]
    
    match_count = 0
    matched_points = []
    unmatched_points = []
    
    for x, y in points:
        actual_color = GetColor(x, y)
        if ColorMatch(actual_color, ref):
            match_count += 1
            matched_points.append((x, y, actual_color))
        else:
            unmatched_points.append((x, y, actual_color))
    
    # 输出详细信息用于调试
    print(f"检查中心点({center_x}, {center_y})，期望颜色: {ref}")
    print(f"匹配的点数: {match_count}/7 (需要至少 {min_matches} 个)")
    print("匹配的点:")
    for x, y, color in matched_points:
        print(f"  ({x}, {y}): {color}")
    print("不匹配的点:")
    for x, y, color in unmatched_points:
        print(f"  ({x}, {y}): {color}")
    
    return match_count >= min_matches


def test_examples():
    """
    测试不同场景的示例
    """
    print("=" * 50)
    print("测试ColorCheck7Points函数")
    print("=" * 50)
    
    # 测试1：正常情况，应该有6个点匹配
    print("\n测试1：正常情况")
    result1 = ColorCheck7Points(1140, 620, "FBFF", min_matches=4)
    print(f"结果: {'通过' if result1 else '失败'}")
    
    # 测试2：提高要求，需要7个点都匹配
    print("\n测试2：要求所有7个点都匹配")
    result2 = ColorCheck7Points(1140, 620, "FBFF", min_matches=7)
    print(f"结果: {'通过' if result2 else '失败'}")
    
    # 测试3：降低要求，只需要3个点匹配
    print("\n测试3：只需要3个点匹配")
    result3 = ColorCheck7Points(1140, 620, "FBFF", min_matches=3)
    print(f"结果: {'通过' if result3 else '失败'}")
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)


def usage_example():
    """
    实际使用示例
    """
    print("\n实际使用示例:")
    print("在游戏检测中的应用:")
    
    # 示例：检查技能是否可用
    if ColorCheck7Points(1140, 620, "FBFF", min_matches=4):
        print("技能可用，执行按键操作")
        # press_key('f9')  # 在实际代码中会执行按键
    else:
        print("技能不可用，跳过")
    
    # 示例：检查不同的颜色和要求
    skill_checks = [
        (1135, 623, "E773", 4),  # 技能1，需要4个点匹配
        (1140, 621, "FBFF", 5),  # 技能2，需要5个点匹配
        (1141, 622, "9C7F", 3),  # 技能3，需要3个点匹配
    ]
    
    for x, y, color, min_matches in skill_checks:
        if ColorCheck7Points(x, y, color, min_matches):
            print(f"位置({x}, {y})的技能检测通过")
        else:
            print(f"位置({x}, {y})的技能检测失败")


if __name__ == "__main__":
    test_examples()
    usage_example()
